import { hygraphClient, GET_ALL_POSTS, GET_POST_BY_SLUG, GET_FEATURED_POSTS, GET_POSTS_BY_CATEGORY, GET_POSTS_BY_TAG, GET_CATEGORIES, GET_TAGS, GET_RECENT_POSTS } from './hygraph'
import { BlogPost, Author } from './blog'

// Transform Hygraph data to our BlogPost interface
function transformHygraphPost(hygraphPost: any): BlogPost {
  return {
    id: hygraphPost.id,
    title: hygraphPost.title,
    slug: hygraphPost.slug,
    excerpt: hygraphPost.excerpt,
    content: hygraphPost.content,
    date: hygraphPost.publishedAt,
    featured: hygraphPost.featured || false,
    coverImage: hygraphPost.coverImage?.url || '/placeholder.svg?height=600&width=1200',
    category: hygraphPost.category?.name || 'Uncategorized',
    tags: hygraphPost.tags?.map((tag: any) => tag.name) || [],
    author: {
      name: hygraphPost.author?.name || 'Anonymous',
      title: hygraphPost.author?.title,
      bio: hygraphPost.author?.bio,
      avatar: hygraphPost.author?.avatar?.url,
      socialLinks: hygraphPost.author?.socialLinks || {}
    }
  }
}

// Fetch all blog posts from Hygraph
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    const data = await hygraphClient.request(GET_ALL_POSTS)
    return data.blogPosts.map(transformHygraphPost)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

// Fetch a single blog post by slug
export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const data = await hygraphClient.request(GET_POST_BY_SLUG, { slug })
    return data.blogPost ? transformHygraphPost(data.blogPost) : null
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return null
  }
}

// Fetch featured posts
export async function getFeaturedPosts(): Promise<BlogPost[]> {
  try {
    const data = await hygraphClient.request(GET_FEATURED_POSTS)
    return data.blogPosts.map(transformHygraphPost)
  } catch (error) {
    console.error('Error fetching featured posts:', error)
    return []
  }
}

// Fetch posts by category
export async function getPostsByCategory(categorySlug: string): Promise<BlogPost[]> {
  try {
    const data = await hygraphClient.request(GET_POSTS_BY_CATEGORY, { categorySlug })
    return data.blogPosts.map(transformHygraphPost)
  } catch (error) {
    console.error('Error fetching posts by category:', error)
    return []
  }
}

// Fetch posts by tag
export async function getPostsByTag(tagSlug: string): Promise<BlogPost[]> {
  try {
    const data = await hygraphClient.request(GET_POSTS_BY_TAG, { tagSlug })
    return data.blogPosts.map(transformHygraphPost)
  } catch (error) {
    console.error('Error fetching posts by tag:', error)
    return []
  }
}

// Fetch recent posts
export async function getRecentPosts(count: number = 3): Promise<BlogPost[]> {
  try {
    const data = await hygraphClient.request(GET_RECENT_POSTS, { limit: count })
    return data.blogPosts.map(transformHygraphPost)
  } catch (error) {
    console.error('Error fetching recent posts:', error)
    return []
  }
}

// Fetch all categories
export async function getCategories(): Promise<Array<{ id: string; name: string; slug: string; description?: string }>> {
  try {
    const data = await hygraphClient.request(GET_CATEGORIES)
    return data.categories
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

// Fetch all tags
export async function getTags(): Promise<Array<{ id: string; name: string; slug: string }>> {
  try {
    const data = await hygraphClient.request(GET_TAGS)
    return data.tags
  } catch (error) {
    console.error('Error fetching tags:', error)
    return []
  }
}

// Search posts (client-side filtering for now)
export async function searchPosts(query: string): Promise<BlogPost[]> {
  try {
    const allPosts = await getAllBlogPosts()
    const searchTerm = query.toLowerCase()
    
    return allPosts.filter(post => 
      post.title.toLowerCase().includes(searchTerm) ||
      post.excerpt.toLowerCase().includes(searchTerm) ||
      post.content.toLowerCase().includes(searchTerm) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
      post.category.toLowerCase().includes(searchTerm)
    )
  } catch (error) {
    console.error('Error searching posts:', error)
    return []
  }
}
