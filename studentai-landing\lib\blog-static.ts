import { getAllBlogPosts } from './blog-service'

// Generate static paths for blog posts
export async function generateBlogStaticPaths() {
  try {
    const posts = await getAllBlogPosts()
    
    return posts.map((post) => ({
      params: {
        slug: post.slug || post.id
      }
    }))
  } catch (error) {
    console.error('Error generating static paths:', error)
    return []
  }
}

// Get static props for a blog post
export async function getBlogStaticProps(slug: string) {
  try {
    const { getBlogPost, getRecentPosts } = await import('./blog-service')
    
    const post = await getBlogPost(slug)
    const recentPosts = await getRecentPosts(3)
    
    if (!post) {
      return {
        notFound: true
      }
    }
    
    return {
      props: {
        post,
        recentPosts: recentPosts.filter(p => p.id !== post.id)
      },
      // Revalidate every hour
      revalidate: 3600
    }
  } catch (error) {
    console.error('Error getting static props:', error)
    return {
      notFound: true
    }
  }
}
