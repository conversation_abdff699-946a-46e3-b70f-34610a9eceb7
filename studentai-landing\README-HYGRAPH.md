# Hygraph (GraphCMS) Integration

This project now includes a complete Hygraph (formerly GraphCMS) integration for managing blog content as a headless CMS.

## 🚀 Features

- **Headless CMS**: Manage blog content through Hygraph's intuitive interface
- **GraphQL API**: Efficient data fetching with GraphQL queries
- **Real-time Search**: Client-side search functionality with debounced queries
- **SEO Optimized**: Dynamic metadata generation for blog posts
- **Responsive Design**: Mobile-friendly blog layout
- **Automatic Revalidation**: Webhook support for content updates
- **Error Handling**: Graceful fallbacks when CMS is unavailable
- **TypeScript Support**: Fully typed GraphQL responses

## 📁 File Structure

```
lib/
├── hygraph.ts              # GraphQL client and queries
├── blog-service.ts         # Blog data fetching functions
├── blog-static.ts          # Static generation utilities
└── blog.ts                 # Type definitions

app/
├── blog/
│   ├── page.tsx           # Blog listing page
│   └── [slug]/page.tsx    # Individual blog post page
└── api/
    ├── blog/
    │   ├── route.ts       # Blog API endpoint
    │   └── [slug]/route.ts # Single post API endpoint
    └── revalidate/route.ts # Webhook handler

components/blog/
├── blog-search.tsx        # Search component
├── post-card.tsx          # Blog post card
├── blog-sidebar.tsx       # Sidebar component
└── newsletter-section.tsx # Newsletter signup

hooks/
└── use-debounce.ts        # Debounce hook for search

docs/
└── hygraph-setup.md       # Detailed setup guide
```

## 🛠️ Setup Instructions

### 1. Install Dependencies

The required dependencies are already installed:
- `graphql-request`: GraphQL client
- `graphql`: GraphQL core library

### 2. Environment Variables

Create a `.env.local` file with your Hygraph credentials:

```env
HYGRAPH_ENDPOINT=https://your-region.hygraph.com/v2/your-project-id/master
HYGRAPH_TOKEN=your-permanent-auth-token
HYGRAPH_WEBHOOK_SECRET=your-webhook-secret (optional)
```

### 3. Hygraph Schema Setup

Follow the detailed guide in `docs/hygraph-setup.md` to:
- Create the required content models (BlogPost, Author, Category, Tag)
- Set up API permissions
- Add sample content

### 4. Test the Integration

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Visit `/blog` to see the blog listing
3. Test the search functionality
4. Create a blog post in Hygraph and verify it appears

## 🔧 Configuration

### GraphQL Queries

All GraphQL queries are defined in `lib/hygraph.ts`:
- `GET_ALL_POSTS`: Fetch all blog posts
- `GET_POST_BY_SLUG`: Fetch single post by slug
- `GET_FEATURED_POSTS`: Fetch featured posts
- `GET_POSTS_BY_CATEGORY`: Filter by category
- `GET_POSTS_BY_TAG`: Filter by tag
- `GET_CATEGORIES`: Fetch all categories
- `GET_TAGS`: Fetch all tags

### API Endpoints

- `GET /api/blog`: List all posts with optional filtering
  - Query params: `q` (search), `category`, `tag`, `limit`
- `GET /api/blog/[slug]`: Get single post by slug
- `POST /api/revalidate`: Webhook for content updates

### Search Functionality

The search component (`components/blog/blog-search.tsx`) provides:
- Real-time search with 300ms debounce
- Dropdown results with post previews
- Integration with the blog API

## 🎨 Customization

### Styling

The blog components use Tailwind CSS and are fully customizable:
- Modify `components/blog/post-card.tsx` for post appearance
- Update `app/blog/page.tsx` for layout changes
- Customize search UI in `components/blog/blog-search.tsx`

### Content Types

To add new content types:
1. Create the model in Hygraph
2. Add GraphQL queries in `lib/hygraph.ts`
3. Update the service functions in `lib/blog-service.ts`
4. Create new components as needed

### SEO

SEO metadata is automatically generated from:
- Post title and excerpt
- Author information
- Publication date
- Cover images
- Tags and categories

## 🔄 Content Management Workflow

1. **Create Content**: Use Hygraph's rich text editor
2. **Preview**: Content is immediately available via API
3. **Publish**: Set publication status and date
4. **Update**: Changes trigger automatic revalidation (with webhooks)

## 🚀 Deployment

### Environment Variables

Set these in your production environment:
- `HYGRAPH_ENDPOINT`
- `HYGRAPH_TOKEN`
- `HYGRAPH_WEBHOOK_SECRET` (optional)

### Webhooks (Optional)

Set up webhooks in Hygraph to automatically revalidate content:
1. Go to Hygraph Settings > Webhooks
2. Add webhook URL: `https://yourdomain.com/api/revalidate`
3. Set secret header: `x-webhook-secret`
4. Configure triggers for create/update/delete operations

### Performance

- Uses Next.js App Router for optimal performance
- Implements proper error boundaries
- Includes loading states and fallbacks
- Supports ISR (Incremental Static Regeneration)

## 🐛 Troubleshooting

### Common Issues

1. **GraphQL Errors**: Check your endpoint URL and token
2. **CORS Issues**: Ensure your domain is whitelisted in Hygraph
3. **Rate Limits**: Monitor API usage in Hygraph dashboard
4. **Build Errors**: Verify all environment variables are set

### Debug Mode

Enable debug logging by adding to your environment:
```env
DEBUG=hygraph:*
```

## 📚 Resources

- [Hygraph Documentation](https://hygraph.com/docs)
- [GraphQL Documentation](https://graphql.org/learn/)
- [Next.js App Router](https://nextjs.org/docs/app)
- [Tailwind CSS](https://tailwindcss.com/docs)
