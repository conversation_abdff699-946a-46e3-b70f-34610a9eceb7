import { GraphQLClient } from 'graphql-request'

// Initialize Hygraph client
const hygraphClient = new GraphQLClient(
  process.env.HYGRAPH_ENDPOINT || '',
  {
    headers: {
      Authorization: `Bearer ${process.env.HYGRAPH_TOKEN || ''}`,
    },
  }
)

// GraphQL queries
export const GET_ALL_POSTS = `
  query GetAllPosts {
    blogPosts(orderBy: publishedAt_DESC) {
      id
      title
      slug
      excerpt
      content
      publishedAt
      featured
      coverImage {
        url
        width
        height
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
        }
        socialLinks {
          twitter
          linkedin
          website
        }
      }
    }
  }
`

export const GET_POST_BY_SLUG = `
  query GetPostBySlug($slug: String!) {
    blogPost(where: { slug: $slug }) {
      id
      title
      slug
      excerpt
      content
      publishedAt
      featured
      coverImage {
        url
        width
        height
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
        }
        socialLinks {
          twitter
          linkedin
          website
        }
      }
    }
  }
`

export const GET_FEATURED_POSTS = `
  query GetFeaturedPosts {
    blogPosts(where: { featured: true }, orderBy: publishedAt_DESC) {
      id
      title
      slug
      excerpt
      publishedAt
      coverImage {
        url
        width
        height
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
        }
      }
    }
  }
`

export const GET_POSTS_BY_CATEGORY = `
  query GetPostsByCategory($categorySlug: String!) {
    blogPosts(where: { category: { slug: $categorySlug } }, orderBy: publishedAt_DESC) {
      id
      title
      slug
      excerpt
      publishedAt
      coverImage {
        url
        width
        height
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
        }
      }
    }
  }
`

export const GET_POSTS_BY_TAG = `
  query GetPostsByTag($tagSlug: String!) {
    blogPosts(where: { tags_some: { slug: $tagSlug } }, orderBy: publishedAt_DESC) {
      id
      title
      slug
      excerpt
      publishedAt
      coverImage {
        url
        width
        height
      }
      category {
        name
        slug
      }
      tags {
        name
        slug
      }
      author {
        name
        title
        bio
        avatar {
          url
        }
      }
    }
  }
`

export const GET_CATEGORIES = `
  query GetCategories {
    categories(orderBy: name_ASC) {
      id
      name
      slug
      description
    }
  }
`

export const GET_TAGS = `
  query GetTags {
    tags(orderBy: name_ASC) {
      id
      name
      slug
    }
  }
`

export const GET_RECENT_POSTS = `
  query GetRecentPosts($limit: Int = 3) {
    blogPosts(first: $limit, orderBy: publishedAt_DESC) {
      id
      title
      slug
      excerpt
      publishedAt
      coverImage {
        url
        width
        height
      }
      category {
        name
        slug
      }
      author {
        name
        title
      }
    }
  }
`

// Export the client for direct use if needed
export { hygraphClient }
