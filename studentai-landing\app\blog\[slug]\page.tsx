import { BlogSidebar } from "@/components/blog/blog-sidebar"
import { PostContent } from "@/components/blog/post-content"
import { Footer } from "@/components/footer"
import { Navbar } from "@/components/navbar"
import { <PERSON>ton } from "@/components/ui/button"
import { getBlogPost, getRecentPosts } from "@/lib/blog-service"
import { ArrowLeft } from "lucide-react"
import type { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getBlogPost(params.slug)

  if (!post) {
    return {
      title: "Blog Post Not Found",
    }
  }

  return {
    title: `${post.title} | StudentAidDetector Blog`,
    description: post.excerpt,
    keywords: post.tags,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.date,
      authors: [post.author.name],
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const post = await getBlogPost(params.slug)

  if (!post) {
    notFound()
  }

  const recentPosts = (await getRecentPosts(3)).filter((p) => p.id !== post.id)

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-12 flex-grow">
        <Link href="/blog" className="inline-block mb-8">
          <Button variant="ghost" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to all posts
          </Button>
        </Link>

        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {post.coverImage && (
              <Image
                src={post.coverImage}
                alt={post.title}
                width={1200}
                height={600}
                className="w-full h-auto object-cover rounded-lg mb-8"
              />
            )}
            <PostContent post={post} />
          </div>

          <div>
            <BlogSidebar recentPosts={recentPosts} />
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
