'use client'

import { useState, useEffect, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Search, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { BlogPost } from '@/lib/blog'
import { PostCard } from './post-card'
import { useDebounce } from '@/hooks/use-debounce'

interface BlogSearchProps {
  onResults?: (results: BlogPost[]) => void
  placeholder?: string
  className?: string
}

export function BlogSearch({ onResults, placeholder = "Search articles...", className }: BlogSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  
  const debouncedQuery = useDebounce(query, 300)

  const searchPosts = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      setShowResults(false)
      onResults?.([])
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/blog?q=${encodeURIComponent(searchQuery)}`)
      const data = await response.json()
      
      if (data.success) {
        setResults(data.data)
        setShowResults(true)
        onResults?.(data.data)
      }
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }, [onResults])

  useEffect(() => {
    searchPosts(debouncedQuery)
  }, [debouncedQuery, searchPosts])

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setShowResults(false)
    onResults?.([])
  }

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
        <Input
          type="search"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-10 pr-10 py-6 text-lg"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-background border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-muted-foreground">
              Searching...
            </div>
          ) : results.length > 0 ? (
            <div className="p-4">
              <div className="text-sm text-muted-foreground mb-3">
                Found {results.length} result{results.length !== 1 ? 's' : ''}
              </div>
              <div className="space-y-3">
                {results.slice(0, 5).map((post) => (
                  <div key={post.id} className="border-b last:border-b-0 pb-3 last:pb-0">
                    <PostCard post={post} variant="compact" />
                  </div>
                ))}
                {results.length > 5 && (
                  <div className="text-sm text-muted-foreground text-center pt-2">
                    And {results.length - 5} more results...
                  </div>
                )}
              </div>
            </div>
          ) : query.trim() ? (
            <div className="p-4 text-center text-muted-foreground">
              No articles found for "{query}"
            </div>
          ) : null}
        </div>
      )}
    </div>
  )
}
