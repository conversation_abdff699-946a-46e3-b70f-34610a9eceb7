import { NextRequest, NextResponse } from 'next/server'
import { getAllBlogPosts, searchPosts } from '@/lib/blog-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const category = searchParams.get('category')
    const tag = searchParams.get('tag')
    const limit = searchParams.get('limit')

    let posts = await getAllBlogPosts()

    // Apply search filter
    if (query) {
      posts = await searchPosts(query)
    }

    // Apply category filter
    if (category) {
      posts = posts.filter(post => 
        post.category.toLowerCase() === category.toLowerCase()
      )
    }

    // Apply tag filter
    if (tag) {
      posts = posts.filter(post => 
        post.tags.some(postTag => 
          postTag.toLowerCase() === tag.toLowerCase()
        )
      )
    }

    // Apply limit
    if (limit) {
      const limitNum = parseInt(limit, 10)
      if (!isNaN(limitNum)) {
        posts = posts.slice(0, limitNum)
      }
    }

    return NextResponse.json({
      success: true,
      data: posts,
      count: posts.length
    })
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch blog posts' 
      },
      { status: 500 }
    )
  }
}
