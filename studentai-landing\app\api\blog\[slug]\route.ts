import { NextRequest, NextResponse } from 'next/server'
import { getBlogPost } from '@/lib/blog-service'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const post = await getBlogPost(params.slug)

    if (!post) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Blog post not found' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: post
    })
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch blog post' 
      },
      { status: 500 }
    )
  }
}
